// React core
// Third-party library imports
import { Image, Mic } from "microapps";
// Services
// Components
import { SimpleVoiceChat } from "../../SimpleVoiceChat";
// Utils & Constants & Helpers
// Styles

interface GameProps {
  generatedCharacter: string;
  gameStarted: boolean;
  isGameStarted: boolean;
  initialMessage: string;
  onGameEnd: (gameWon: boolean) => void;
  onShowGameLive: () => void;
  onShowGameHint: () => void;
  onShowGameExit: () => void;
}

export const Game: React.FC<GameProps> = ({
  generatedCharacter,
  gameStarted,
  isGameStarted,
  initialMessage,
  onGameEnd,
  onShowGameLive,
  onShowGameHint,
  onShowGameExit,
}) => {
  return (
    <div className="view-main-menu">
      <div className="container">
        <div className="enygma-logo">
          <Image
            src="assets/game/enygma.png"
            alt="Enygma"
            className="enygma-image"
            width="180px"
            aspectRatio="1:1"
          />

          <div className="speaking">
            {/* <Mic
              level={Math.round(micLevel * 100)}
              onClick={toggleVoice}
              state={
                isVoiceActive
                  ? "recording"
                  : voiceError
                  ? "disabled"
                  : "default"
              }
            />
            {voiceError && (
              <div
                className="voice-error"
                style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
              >
                {voiceError}
              </div>
            )}*/}
          </div>
        </div>

        <SimpleVoiceChat
          generatedCharacter={generatedCharacter}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
          onGameEnd={onGameEnd}
        />

        {(generatedCharacter || gameStarted) && (
          <>
            <div className="game-navigation">
              <button onClick={onShowGameLive} className="nav-button">
                ❤️ Vidas
              </button>

              <button onClick={onShowGameHint} className="nav-button">
                💡 Pistas
              </button>

              <button onClick={onShowGameExit} className="nav-button">
                🚪 Salir
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
